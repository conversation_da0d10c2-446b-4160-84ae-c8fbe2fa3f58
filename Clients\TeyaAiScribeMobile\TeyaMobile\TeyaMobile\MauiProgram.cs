using Syncfusion.Licensing;
using Microsoft.Extensions.Logging;
using TeyaMobile.Services;
using TeyaMobile.Shared.Services;
using CommunityToolkit.Maui;
using Syncfusion.Maui.Core.Hosting;
using Syncfusion.Maui.Popup;
using CommunityToolkit.Maui.Core;
using Microsoft.JSInterop;
using TeyaUIViewModels.ViewModel;
using System.Reflection;
using Microsoft.Extensions.Configuration;
using Microsoft.Maui.Controls.Hosting;
using Microsoft.Maui.Hosting;
using TeyaMobileModel.Model;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Localization;
using Microsoft.AspNetCore.Http;
using TeyaMobileViewModel.ViewModel;
using System;
using System.Net.Http;
using TeyaMobile.Shared.Pages;
using Microsoft.Identity.Client;
using Microsoft.Extensions.Options;
using Syncfusion.Blazor;
using TeyaWebApp.ViewModel;
using TeyaMobileModel.ViewModel;



#if ANDROID
using TeyaMobile.Platforms;
#elif IOS
using TeyaMobile.Platforms;
#endif

namespace TeyaMobile
{
    public static class MauiProgram
    {
        public static MauiApp CreateMauiApp()
        {

            SyncfusionLicenseProvider.RegisterLicense("Mzg4OTIzM0AzMjM5MmUzMDJlMzAzYjMyMzkzYlJwa09TaVM0bUVZdnhmOGFWR0plYWd0TFFTQzFSNjRmV0JSTkpJTlBQMlk9;Mgo+DSMBMAY9C3t2XFhhQlJHfVldX2pWfFN0QHNYf1R0dV9EYUwgOX1dQl9mSXhTdEVgWH5beX1dQ2VXU00=;Mgo+DSMBPh8sVXJ9S0d+X1JPcEBAVHxLflFzVWJZdVpxfldHcC0sT3RfQFhjT35RdkZiW39ecHdXRWteWA==;Mzg4OTIzNkAzMjM5MmUzMDJlMzAzYjMyMzkzYlMvRHlQMUFCOVV6VU1BVE0rMjdIUjFYallkQXhoY1lVQmVVNVpHNUZXblU9;NRAiBiAaIQQuGjN/V09+XU9HdVREQmFAYVF2R2ZJfl56cFRMYl1BNQtUQF1hTH5VdkdjWn5ccHRURGdaWkZ/;ORg4AjUWIQA/Gnt2XFhhQlJHfVldX2pWfFN0QHNYf1R0dV9EYUwgOX1dQl9mSXhTdEVgWH1ecHVRRWJXU00=;Mzg4OTIzOUAzMjM5MmUzMDJlMzAzYjMyMzkzYlJwa09TaVM0bUVZdnhmOGFWR0plYWd0TFFTQzFSNjRmV0JSTkpJTlBQMlk9");
            var builder = MauiApp.CreateBuilder();

            builder
                .UseMauiApp<App>()
                .ConfigureSyncfusionCore()
                .UseMauiCommunityToolkit()
                .UseMauiCommunityToolkitMediaElement()
                .UseMauiCommunityToolkitCore()
                .ConfigureFonts(fonts =>
                {
                    fonts.AddFont("OpenSans-Regular.ttf", "OpenSans");
                    fonts.AddFont("OpenSans-Semibold.ttf", "OpenSansSemibold");
                    fonts.AddFont("FluentSystemIcons-Filled.ttf", "FluentIcons");
                    fonts.AddFont("MaterialIcons-Regular.ttf", "MaterialIcons");
                })
                .UseMauiCommunityToolkit();

            builder.Logging.ClearProviders();
            builder.Logging.AddDebug();

            // Load environment variables from .env file
            DotNetEnv.Env.Load();

            builder.Services.AddSyncfusionBlazor();

            using var stream = FileSystem.OpenAppPackageFileAsync("appsettings.json").Result;
            builder.Configuration.AddJsonStream(stream);

            foreach (var setting in builder.Configuration.AsEnumerable())
            {
                if (!string.IsNullOrEmpty(setting.Key) && setting.Value != null)
                {
                    Environment.SetEnvironmentVariable(setting.Key, setting.Value);
                }
            }

            builder.Services.AddLocalization();

            // MSAL Configuration
            builder.Services.AddSingleton<IPublicClientApplication>(provider =>
            {
                var configuration = provider.GetRequiredService<IConfiguration>();
                var azureAdSection = configuration.GetSection("AzureAd");

                var clientId = azureAdSection["ClientId"];
                var instance = azureAdSection["Instance"];
                var tenantId = azureAdSection["TenantId"];

                var appBuilder = PublicClientApplicationBuilder
                    .Create(clientId)
                    .WithAuthority($"{instance}{tenantId}")
                    .WithRedirectUri($"msal{clientId}://auth");

#if ANDROID
                appBuilder = appBuilder.WithParentActivityOrWindow(() => Microsoft.Maui.ApplicationModel.Platform.CurrentActivity);
#elif IOS
                appBuilder = appBuilder.WithIosKeychainSecurityGroup("com.companyname.teyamobile");
#endif

                return appBuilder.Build();
            });

            // Authentication Services
            builder.Services.AddSingleton<TeyaMobileViewModel.ViewModel.IAuthenticationService, SimpleAndroidAuthenticationService>();
            builder.Services.AddAuthorizationCore();

            builder.Services.AddSingleton<AppointmentsViewModel>();
            builder.Services.AddTransient<MainPage>();
            builder.Services.AddTransient<TeyaAI>();
            builder.Services.AddTransient<Message>();
            builder.Services.AddTransient<TemplatesPage>();
            builder.Services.AddTransient<PatientHome>();
            builder.Services.AddTransient<ProviderHome>();
            builder.Services.AddTransient<AppointmentsDoctor>();

            builder.Services.AddSingleton<NavigationManager>();
            builder.Services.AddLocalization();

            builder.Services.AddSingleton<IConfiguration>(builder.Configuration);
            builder.Services.AddSingleton<ISpeechService, SpeechService>();
            builder.Services.AddSingleton<IProgressNotesService, ProgressNotesService>();
            builder.Services.AddSingleton<PatientService>();
            builder.Services.AddSingleton<IAppointmentService, AppointmentService>();
            // Configure HttpClient for Android platform - NO BaseAddress for absolute URLs
            builder.Services.AddSingleton<HttpClient>(provider =>
            {
                var httpClient = new HttpClient();
                httpClient.Timeout = TimeSpan.FromSeconds(30);
                // DO NOT set BaseAddress - services use absolute URLs like https://graph.microsoft.com/v1.0/me
                return httpClient;
            });
            builder.Services.AddSingleton<ActiveUser>();
            builder.Services.AddTransient<AudioRecorderComponent>();
            builder.Services.AddScoped<TeyaMobileModel.ViewModel.IOrganizationService, OrganizationService>();
            builder.Services.AddScoped<TeyaMobileModel.Model.ActiveUser>();
            builder.Services.AddScoped<TeyaUIViewModels.ViewModel.IRoleslistService, RoleslistService>();
            builder.Services.AddScoped<TeyaMobileModel.ViewModel.IRoleService, RoleService>();
            builder.Services.AddScoped<GraphApiService>();
            builder.Services.AddScoped<IMemberService, MemberService>();
            builder.Services.AddScoped<TeyaMobileViewModel.ViewModel.IGraphAdminService, GraphAdminService>();
            builder.Services.AddSingleton<IFormFactor, FormFactor>();

#if ANDROID
            builder.Services.AddSingleton<IAudioRecorder, TeyaMobile.Platforms.Android.Services.AndroidAudioRecorderService>();
#elif IOS
        builder.Services.AddSingleton<IAudioRecorder, TeyaMobile.Platforms.iOS.Services.IOSAudioRecorderService>();
#endif

            // Register platform-specific services
#if ANDROID
            builder.Services.AddSingleton<TeyaMobileViewModel.ViewModel.IAudioRecorder, TeyaMobile.Platforms.Android.Services.AndroidAudioRecorderService>();

#elif IOS
            builder.Services.AddSingleton<TeyaMobileViewModel.ViewModel.IAudioRecorder, TeyaMobile.Platforms.iOS.Services.IOSAudioRecorderService>();
#endif

            // Add device-specific services used by the TeyaMobile.Shared project
            builder.Services.AddSingleton<IFormFactor, FormFactor>();

            builder.Services.AddMauiBlazorWebView();

#if DEBUG
            builder.Services.AddBlazorWebViewDeveloperTools();
            builder.Logging.AddDebug();
#endif

            return builder.Build();
        }
    }
}
